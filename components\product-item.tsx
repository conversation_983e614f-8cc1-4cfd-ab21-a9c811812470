"use client"

import { useState, useEffect } from "react"
import { Plus, Minus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { useRealtimeCart } from "@/context/realtime-cart-context"
import FallbackImage from "@/components/fallback-image"
import ProductAttributes from "@/components/product/ProductAttributes"
import { supabase } from "@/lib/supabase"
import type { BusinessType } from "@/types/cart"
import { cn } from "@/lib/utils"

// Unified customization types
interface CustomizationOption {
  id: string;
  name: string;
  price: number;
}

interface Customization {
  id: string;
  name: string;
  required: boolean;
  isMultiple: boolean;
  options: CustomizationOption[];
}

// Unified product interface
interface UnifiedProduct {
  id: string;
  name: string;
  description?: string;
  price: number;
  image?: string;
  isPopular?: boolean;
  quantity?: string | number;
  unit?: string;
  customizations?: Customization[];
  attributes?: {
    dietary?: string[];
    allergen?: string[];
    spice_level?: string[];
    prep_time_minutes?: string[];
    popularity?: string[];
    [key: string]: string[] | undefined;
  };
  inStock?: boolean;
  variants?: Array<{
    id?: string;
    name: string;
    price?: number;
    priceAdjustment?: number;
  }>;
  requiresPrescription?: boolean;
}

interface ProductItemProps {
  product: UnifiedProduct;
  businessId: number; // Changed from string to number to match database schema
  businessSlug?: string; // Added businessSlug for routing
  businessName?: string;
  businessType: BusinessType;
  categoryId: string;
  layout?: 'horizontal' | 'vertical' | 'compact' | 'slim' | 'aisle';
}

export default function ProductItem({
  product,
  businessId,
  businessSlug,
  businessName,
  businessType,
  categoryId,
  layout = 'horizontal'
}: ProductItemProps) {
  const { addToCart, getItemQuantity, updateQuantity, getDeliveryMethod } = useRealtimeCart()
  const [quantity, setQuantity] = useState(1)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedOptions, setSelectedOptions] = useState<Record<string, string[]>>({})
  const [selectedVariant, setSelectedVariant] = useState<any>(null)
  const [totalPrice, setTotalPrice] = useState(product.price)
  const [minimumOrderAmount, setMinimumOrderAmount] = useState<number | null>(null)

  const itemQuantityInCart = getItemQuantity(product.id)
  const isInCart = itemQuantityInCart > 0

  // Fetch minimum order amount when component mounts (for restaurants)
  useEffect(() => {
    if (businessType === 'restaurant' || businessType === 'cafe') {
      const fetchMinimumOrderAmount = async () => {
        try {
          // Use the numeric business ID directly
          const { data, error } = await supabase
            .from('businesses')
            .select('minimum_order_amount')
            .eq('id', businessId)
            .single();

          if (!error && data) {
            setMinimumOrderAmount(data.minimum_order_amount);
          }
        } catch (error) {
          console.error('Error fetching minimum order amount:', error);
        }
      };

      fetchMinimumOrderAmount();
    }
  }, [businessId, businessType]);

  const handleAddToCart = (options?: string[]) => {
    // Ensure businessId is a number
    const numericBusinessId = typeof businessId === 'number'
      ? businessId
      : !isNaN(Number(businessId))
        ? Number(businessId)
        : null;

    if (numericBusinessId === null) {
      console.error(`Invalid business ID: ${businessId}. Cannot add item to cart.`);
      return;
    }

    // Get the current delivery method for this business
    const deliveryMethod = getDeliveryMethod(numericBusinessId) || 'delivery';

    // Prepare customizations data for cart
    const customizations = product.customizations?.map(customization => {
      const selectedOptionIds = selectedOptions[customization.id] || [];
      const selectedCustomizationOptions = customization.options.filter(option =>
        selectedOptionIds.includes(option.id)
      );

      return {
        groupId: parseInt(customization.id),
        groupName: customization.name,
        options: selectedCustomizationOptions.map(option => ({
          id: parseInt(option.id),
          name: option.name,
          price: option.price
        }))
      };
    }).filter(customization => customization.options.length > 0) || [];



    addToCart({
      id: product.id,
      businessId: numericBusinessId, // Ensure it's a number
      // REMOVED: businessSlug - no longer stored in cart_items table
      businessName: businessName, // Use provided business name
      businessType: businessType,
      categoryId,
      name: product.name,
      price: totalPrice,
      quantity: quantity,
      variantId: selectedVariant ? parseInt(selectedVariant.id) : undefined, // Pass selected variant ID
      variantName: selectedVariant?.name, // Pass selected variant name
      customizations: customizations.length > 0 ? customizations : undefined, // Pass customizations
      imageUrl: product.image, // Add the missing imageUrl field
      options: options || (product.unit && product.quantity ? [`${product.quantity} ${product.unit}`] : undefined),
      minimumOrderAmount: minimumOrderAmount || undefined,
      deliveryMethod: deliveryMethod, // Include the delivery method
    })

    setIsDialogOpen(false)
  }

  const incrementQuantity = () => {
    if (isInCart) {
      updateQuantity(product.id, itemQuantityInCart + 1)
    } else {
      setQuantity((prev) => prev + 1)
    }
  }

  const decrementQuantity = () => {
    if (isInCart) {
      if (itemQuantityInCart > 1) {
        updateQuantity(product.id, itemQuantityInCart - 1)
      }
    } else if (quantity > 1) {
      setQuantity((prev) => prev - 1)
    }
  }

  const handleOpenDialog = () => {
    // Reset selections when opening dialog
    const initialSelections: Record<string, string[]> = {}

    // Pre-select required radio options with the first option
    if (product.customizations) {
      product.customizations.forEach((customization) => {
        if (customization.required && !customization.isMultiple) {
          initialSelections[customization.id] = [customization.options[0].id]
        } else {
          initialSelections[customization.id] = []
        }
      })
    }

    // Initialize variant selection with default variant
    if (hasVariants) {
      const defaultVariant = product.variants?.find(v => v.isDefault) || product.variants?.[0];
      setSelectedVariant(defaultVariant);
    }

    setSelectedOptions(initialSelections)
    calculateTotalPrice(initialSelections)
    setIsDialogOpen(true)
  }

  const handleOptionChange = (customizationId: string, optionId: string, isMultiple: boolean) => {
    setSelectedOptions((prev) => {
      const newSelections = { ...prev }

      if (isMultiple) {
        // For checkboxes (multiple selection)
        if (newSelections[customizationId]?.includes(optionId)) {
          newSelections[customizationId] = newSelections[customizationId].filter(id => id !== optionId)
        } else {
          newSelections[customizationId] = [...(newSelections[customizationId] || []), optionId]
        }
      } else {
        // For radio buttons (single selection)
        newSelections[customizationId] = [optionId]
      }

      calculateTotalPrice(newSelections)
      return newSelections
    })
  }

  const calculateTotalPrice = (selections: Record<string, string[]>) => {
    let price = product.price

    // Add variant price if selected
    if (selectedVariant && selectedVariant.price) {
      price = selectedVariant.price;
    }

    if (product.customizations) {
      product.customizations.forEach((customization) => {
        const selectedOptionIds = selections[customization.id] || []

        customization.options.forEach((option) => {
          if (selectedOptionIds.includes(option.id)) {
            price += option.price
          }
        })
      })
    }

    setTotalPrice(price)
  }

  const getSelectedOptionsText = () => {
    const options: string[] = []

    if (product.customizations) {
      product.customizations.forEach((customization) => {
        const selectedOptionIds = selectedOptions[customization.id] || []

        if (selectedOptionIds.length > 0) {
          const selectedOptionNames = customization.options
            .filter((option) => selectedOptionIds.includes(option.id))
            .map((option) => {
              if (option.price > 0) {
                return `${option.name} (+£${option.price.toFixed(2)})`
              }
              return option.name
            })

          options.push(`${customization.name}: ${selectedOptionNames.join(', ')}`)
        }
      })
    }

    return options
  }

  // Determine if we should show the customization dialog
  const hasCustomizations = product.customizations && product.customizations.length > 0;
  const hasVariants = product.variants && product.variants.length > 0;
  // Always show dialog if product has customizations or variants, regardless of required status
  const needsDialog = hasCustomizations || hasVariants;





  // Render different layouts based on the layout prop and business type
  if (layout === 'vertical') {
    // Vertical layout (similar to pharmacy-product.tsx)
    return (
      <>
        <Card className="overflow-hidden">
          {product.image && (
            <div className="relative h-48 w-full">
              <FallbackImage
                src={product.image}
                alt={product.name}
                fallbackSrc="/placeholder.svg"
                className="w-full h-full object-contain"
              />
              {product.isPopular && (
                <div className="absolute top-2 right-2 bg-yellow-400 text-xs font-bold px-2 py-1 rounded">
                  Popular
                </div>
              )}
            </div>
          )}
          <div className="p-4">
            <h3 className="font-medium">{product.name}</h3>
            {product.description && (
              <p className="text-sm text-gray-500 mt-1">{product.description}</p>
            )}
            {product.quantity && product.unit && (
              <p className="text-xs text-gray-500 mt-1">
                {product.quantity} {product.unit}
              </p>
            )}
            {product.attributes && (
              <ProductAttributes attributes={product.attributes} className="mt-1" />
            )}
            <div className="flex justify-between items-center mt-4">
              <p className="font-medium">£{product.price.toFixed(2)}</p>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  onClick={decrementQuantity}
                  disabled={quantity <= 1 && !isInCart}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <span className="w-6 text-center">{isInCart ? itemQuantityInCart : quantity}</span>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  onClick={incrementQuantity}
                >
                  <Plus className="h-4 w-4" />
                </Button>
                <Button
                  onClick={needsDialog ? handleOpenDialog : handleAddToCart}
                  className={isInCart ? "bg-gray-200 text-gray-800 hover:bg-gray-300" : ""}
                >
                  {isInCart ? "Update" : "Add"}
                </Button>
              </div>
            </div>
          </div>
        </Card>

        {/* Customization Dialog */}
        {needsDialog && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{product.name}</DialogTitle>
              </DialogHeader>
              <div className="space-y-6 py-4">
                {/* Variant Selection */}
                {hasVariants && (
                  <div className="space-y-4">
                    <h3 className="font-medium">Size</h3>
                    <RadioGroup
                      value={selectedVariant?.id?.toString() || ""}
                      onValueChange={(value) => {
                        const variant = product.variants?.find(v => v.id?.toString() === value);
                        setSelectedVariant(variant);
                        calculateTotalPrice(selectedOptions);
                      }}
                    >
                      {product.variants?.map((variant) => (
                        <div key={variant.id} className="flex items-center space-x-2">
                          <RadioGroupItem
                            value={variant.id?.toString() || ""}
                            id={`variant-${variant.id}`}
                          />
                          <Label htmlFor={`variant-${variant.id}`} className="flex-1">
                            {variant.name}
                            <span className="text-gray-500 ml-1">
                              £{variant.price?.toFixed(2)}
                            </span>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>
                )}

                {/* Customizations */}
                {product.customizations?.map((customization) => (
                  <div key={customization.id} className="space-y-4">
                    <h3 className="font-medium">
                      {customization.name}
                      {customization.required && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </h3>
                    {customization.isMultiple ? (
                      // Checkbox group for multiple selection
                      <div className="space-y-2">
                        {customization.options.map((option) => (
                          <div key={option.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`${customization.id}-${option.id}`}
                              checked={selectedOptions[customization.id]?.includes(option.id) || false}
                              onCheckedChange={() => handleOptionChange(customization.id, option.id, true)}
                            />
                            <Label htmlFor={`${customization.id}-${option.id}`} className="flex-1">
                              {option.name}
                              {option.price > 0 && (
                                <span className="text-gray-500 ml-1">
                                  +£{option.price.toFixed(2)}
                                </span>
                              )}
                            </Label>
                          </div>
                        ))}
                      </div>
                    ) : (
                      // Radio group for single selection
                      <RadioGroup
                        value={selectedOptions[customization.id]?.[0] || ""}
                        onValueChange={(value) => handleOptionChange(customization.id, value, false)}
                      >
                        {customization.options.map((option) => (
                          <div key={option.id} className="flex items-center space-x-2">
                            <RadioGroupItem
                              value={option.id}
                              id={`${customization.id}-${option.id}`}
                            />
                            <Label htmlFor={`${customization.id}-${option.id}`} className="flex-1">
                              {option.name}
                              {option.price > 0 && (
                                <span className="text-gray-500 ml-1">
                                  +£{option.price.toFixed(2)}
                                </span>
                              )}
                            </Label>
                          </div>
                        ))}
                      </RadioGroup>
                    )}
                  </div>
                ))}
              </div>
              <div className="flex justify-between items-center">
                <p className="font-medium">Total: £{totalPrice.toFixed(2)}</p>
                <Button onClick={() => handleAddToCart(getSelectedOptionsText())}>
                  Add to Basket
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </>
    );
  } else if (layout === 'compact') {
    // Compact layout with consistent heights
    return (
      <>
        <div className="bg-white rounded-md border p-4 flex hover:shadow-sm transition-shadow h-full">
          <div className="flex-grow min-w-0 flex flex-col">
            <div className="flex items-start justify-between mb-2">
              <div className="pr-2 flex-grow">
                <h4 className="font-medium text-sm leading-tight mb-1">{product.name}</h4>
                {product.isPopular && (
                  <Badge variant="outline" className="text-orange-500 border-orange-200 bg-orange-50 text-xs mb-1">
                    Popular
                  </Badge>
                )}
              </div>
            </div>

            <div className="flex-grow">
              {product.description && (
                <p className="text-xs text-gray-500 mb-2 leading-relaxed">{product.description}</p>
              )}
              {product.attributes && (
                <ProductAttributes attributes={product.attributes} className="mb-2" />
              )}
            </div>

            <div className="flex items-center justify-between mt-auto">
              <p className="font-medium text-sm">£{product.price.toFixed(2)}</p>
              <Button
                size="sm"
                variant="ghost"
                className="h-8 w-8 rounded-full p-0 text-red-600 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
                onClick={() => {
                  if (needsDialog) {
                    handleOpenDialog();
                  } else {
                    handleAddToCart();
                  }
                }}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {product.image && (
            <div className="ml-3 w-32 h-32 flex-shrink-0 self-start">
              <FallbackImage
                src={product.image}
                alt={product.name}
                fallbackSrc="/placeholder.svg"
                className="w-full h-full object-contain rounded-md"
              />
            </div>
          )}
        </div>

        {/* Customization Dialog */}
        {needsDialog && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{product.name}</DialogTitle>
              </DialogHeader>
              <div className="space-y-6 py-4">
                {/* Variant Selection */}
                {hasVariants && (
                  <div className="space-y-4">
                    <h3 className="font-medium">Size</h3>
                    <RadioGroup
                      value={selectedVariant?.id?.toString() || ""}
                      onValueChange={(value) => {
                        const variant = product.variants?.find(v => v.id?.toString() === value);
                        setSelectedVariant(variant);
                        calculateTotalPrice(selectedOptions);
                      }}
                    >
                      {product.variants?.map((variant) => (
                        <div key={variant.id} className="flex items-center space-x-2">
                          <RadioGroupItem
                            value={variant.id?.toString() || ""}
                            id={`variant-${variant.id}`}
                          />
                          <Label htmlFor={`variant-${variant.id}`} className="flex-1">
                            {variant.name}
                            <span className="text-gray-500 ml-1">
                              £{variant.price?.toFixed(2)}
                            </span>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>
                )}

                {/* Customizations */}
                {product.customizations?.map((customization) => (
                  <div key={customization.id} className="space-y-4">
                    <h3 className="font-medium">
                      {customization.name}
                      {customization.required && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </h3>
                    {customization.isMultiple ? (
                      // Checkbox group for multiple selection
                      <div className="space-y-2">
                        {customization.options.map((option) => (
                          <div key={option.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`${customization.id}-${option.id}`}
                              checked={selectedOptions[customization.id]?.includes(option.id) || false}
                              onCheckedChange={() => handleOptionChange(customization.id, option.id, true)}
                            />
                            <Label htmlFor={`${customization.id}-${option.id}`} className="flex-1">
                              {option.name}
                              {option.price > 0 && (
                                <span className="text-gray-500 ml-1">
                                  +£{option.price.toFixed(2)}
                                </span>
                              )}
                            </Label>
                          </div>
                        ))}
                      </div>
                    ) : (
                      // Radio group for single selection
                      <RadioGroup
                        value={selectedOptions[customization.id]?.[0] || ""}
                        onValueChange={(value) => handleOptionChange(customization.id, value, false)}
                      >
                        {customization.options.map((option) => (
                          <div key={option.id} className="flex items-center space-x-2">
                            <RadioGroupItem
                              value={option.id}
                              id={`${customization.id}-${option.id}`}
                            />
                            <Label htmlFor={`${customization.id}-${option.id}`} className="flex-1">
                              {option.name}
                              {option.price > 0 && (
                                <span className="text-gray-500 ml-1">
                                  +£{option.price.toFixed(2)}
                                </span>
                              )}
                            </Label>
                          </div>
                        ))}
                      </RadioGroup>
                    )}
                  </div>
                ))}
              </div>
              <div className="flex justify-between items-center">
                <p className="font-medium">Total: £{totalPrice.toFixed(2)}</p>
                <Button onClick={() => handleAddToCart(getSelectedOptionsText())}>
                  Add to Basket
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </>
    );
  } else if (layout === 'slim') {
    // Slim layout - shows only product name and add to cart button
    return (
      <>
        <Card className="overflow-hidden">
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div className="flex-grow min-w-0 pr-3">
                <h3 className="font-medium text-sm truncate">{product.name}</h3>
                {product.isPopular && (
                  <Badge variant="outline" className="mt-1 bg-orange-100 text-orange-800 border-orange-200 text-xs">
                    Popular
                  </Badge>
                )}
              </div>

              <div className="flex items-center space-x-2 flex-shrink-0">
                <p className="font-semibold text-sm">£{product.price.toFixed(2)}</p>

                <div className="flex items-center">
                  {(isInCart || quantity > 1) && (
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-7 w-7 rounded-full"
                      onClick={decrementQuantity}
                    >
                      <Minus className="h-3 w-3" />
                    </Button>
                  )}

                  {isInCart ? (
                    <span className="mx-2 font-medium text-sm">{itemQuantityInCart}</span>
                  ) : quantity > 1 ? (
                    <span className="mx-2 font-medium text-sm">{quantity}</span>
                  ) : null}

                  <Button
                    variant="outline"
                    size="icon"
                    className="h-7 w-7 rounded-full"
                    onClick={needsDialog ? handleOpenDialog : (isInCart ? incrementQuantity : handleAddToCart)}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Customization Dialog */}
        {needsDialog && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{product.name}</DialogTitle>
              </DialogHeader>
              <div className="space-y-6 py-4">
                {/* Variant Selection */}
                {hasVariants && (
                  <div className="space-y-4">
                    <h3 className="font-medium">Size</h3>
                    <RadioGroup
                      value={selectedVariant?.id?.toString() || ""}
                      onValueChange={(value) => {
                        const variant = product.variants?.find(v => v.id?.toString() === value);
                        setSelectedVariant(variant);
                        calculateTotalPrice(selectedOptions);
                      }}
                    >
                      {product.variants?.map((variant) => (
                        <div key={variant.id} className="flex items-center space-x-2">
                          <RadioGroupItem
                            value={variant.id?.toString() || ""}
                            id={`variant-${variant.id}`}
                          />
                          <Label htmlFor={`variant-${variant.id}`} className="flex-1">
                            {variant.name}
                            <span className="text-gray-500 ml-1">
                              £{variant.price?.toFixed(2)}
                            </span>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>
                )}

                {/* Customizations */}
                {product.customizations?.map((customization) => (
                  <div key={customization.id} className="space-y-4">
                    <h3 className="font-medium">
                      {customization.name}
                      {customization.required && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </h3>
                    {customization.isMultiple ? (
                      // Checkbox group for multiple selection
                      <div className="space-y-2">
                        {customization.options.map((option) => (
                          <div key={option.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`${customization.id}-${option.id}`}
                              checked={selectedOptions[customization.id]?.includes(option.id) || false}
                              onCheckedChange={() => handleOptionChange(customization.id, option.id, true)}
                            />
                            <Label htmlFor={`${customization.id}-${option.id}`} className="flex-1">
                              {option.name}
                              {option.price > 0 && (
                                <span className="text-gray-500 ml-1">
                                  +£{option.price.toFixed(2)}
                                </span>
                              )}
                            </Label>
                          </div>
                        ))}
                      </div>
                    ) : (
                      // Radio group for single selection
                      <RadioGroup
                        value={selectedOptions[customization.id]?.[0] || ""}
                        onValueChange={(value) => handleOptionChange(customization.id, value, false)}
                      >
                        {customization.options.map((option) => (
                          <div key={option.id} className="flex items-center space-x-2">
                            <RadioGroupItem
                              value={option.id}
                              id={`${customization.id}-${option.id}`}
                            />
                            <Label htmlFor={`${customization.id}-${option.id}`} className="flex-1">
                              {option.name}
                              {option.price > 0 && (
                                <span className="text-gray-500 ml-1">
                                  +£{option.price.toFixed(2)}
                                </span>
                              )}
                            </Label>
                          </div>
                        ))}
                      </RadioGroup>
                    )}
                  </div>
                ))}
              </div>
              <div className="flex justify-between items-center">
                <p className="font-medium">Total: £{totalPrice.toFixed(2)}</p>
                <Button onClick={() => handleAddToCart(getSelectedOptionsText())}>
                  Add to Cart
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </>
    );
  } else if (layout === 'aisle') {
    // Aisle layout - portrait style with image on top, similar to business cards
    return (
      <>
        <Card className="overflow-hidden h-full">
          <div className="flex flex-col h-full">
            {/* Image Container */}
            <div className="product-image-container aspect-[3/2] sm:aspect-[2/1] md:aspect-[3/2]">
              {product.image ? (
                <FallbackImage
                  src={product.image}
                  alt={product.name}
                  fallbackSrc="/placeholder.svg"
                  className="w-full h-full product-image-smart"
                  smartFit={true}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
                  <div className="text-gray-400 text-center">
                    <svg className="w-8 h-8 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="text-xs">No image</span>
                  </div>
                </div>
              )}
              {product.isPopular && (
                <div className="absolute top-2 right-2 bg-yellow-400 text-xs font-bold px-2 py-1 rounded shadow-sm">
                  Popular
                </div>
              )}
            </div>

            {/* Content Container */}
            <CardContent className="p-2 flex-grow flex flex-col">
              <div className="flex-grow">
                <h3 className="font-medium text-xs leading-tight mb-1">{product.name}</h3>
                {product.quantity && product.unit && (
                  <p className="text-xs text-gray-500 mb-1">
                    {product.quantity} {product.unit}
                  </p>
                )}
                {product.description && (
                  <p className="text-xs text-gray-600 line-clamp-1 mb-1">{product.description}</p>
                )}
              </div>

              {/* Price and Controls */}
              <div className="mt-auto">
                <div className="flex justify-between items-center">
                  <p className="font-medium text-xs">£{product.price.toFixed(2)}</p>

                  {isInCart ? (
                    <div className="flex items-center space-x-1">
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-7 w-7"
                        onClick={decrementQuantity}
                      >
                        <Minus className="h-3 w-3" />
                      </Button>
                      <span className="w-8 text-center text-sm font-medium">{itemQuantityInCart}</span>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-7 w-7"
                        onClick={incrementQuantity}
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <Button
                      size="icon"
                      onClick={needsDialog ? handleOpenDialog : handleAddToCart}
                      className="h-7 w-7 bg-emerald-600 hover:bg-emerald-700 text-white"
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </div>
        </Card>

        {/* Dialog for products with options */}
        {needsDialog && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>{product.name}</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                {product.options && product.options.map((option) => (
                  <div key={option.id} className="space-y-2">
                    <Label className="text-sm font-medium">{option.name}</Label>
                    <div className="space-y-1">
                      {option.choices.map((choice) => (
                        <div key={choice.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`${option.id}-${choice.id}`}
                            checked={selectedOptions[option.id]?.includes(choice.id) || false}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedOptions(prev => ({
                                  ...prev,
                                  [option.id]: [...(prev[option.id] || []), choice.id]
                                }))
                              } else {
                                setSelectedOptions(prev => ({
                                  ...prev,
                                  [option.id]: (prev[option.id] || []).filter(id => id !== choice.id)
                                }))
                              }
                            }}
                          />
                          <Label htmlFor={`${option.id}-${choice.id}`} className="text-sm">
                            {choice.name} {choice.price > 0 && `(+£${choice.price.toFixed(2)})`}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={decrementQuantity}
                    disabled={quantity <= 1}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <span className="w-8 text-center">{quantity}</span>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={incrementQuantity}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <p className="font-medium">Total: £{totalPrice.toFixed(2)}</p>
                <Button onClick={() => handleAddToCart(getSelectedOptionsText())}>
                  Add to Basket
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </>
    );
  } else {
    // Default horizontal layout (similar to original product-item.tsx)
    return (
      <>
        <Card className="overflow-hidden">
          <CardContent className="p-0">
            <div className="flex">
              <div className="flex-grow p-4">
                <div className="flex justify-between">
                  <h3 className="font-medium">{product.name}</h3>
                  {product.isPopular && (
                    <Badge variant="outline" className="ml-2 bg-orange-100 text-orange-800 border-orange-200">
                      Popular
                    </Badge>
                  )}
                </div>

                {product.quantity && product.unit && (
                  <p className="text-sm text-gray-500 mt-1">
                    {product.quantity} {product.unit}
                  </p>
                )}

                {product.description && <p className="text-sm text-gray-500 mt-1 line-clamp-2">{product.description}</p>}

                {product.attributes && (
                  <ProductAttributes attributes={product.attributes} className="mt-1" />
                )}

                <div className="flex items-center justify-between mt-3">
                  <p className="font-semibold">£{product.price.toFixed(2)}</p>

                  <div className="flex items-center">
                    {(isInCart || quantity > 1) && (
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-7 w-7"
                        onClick={decrementQuantity}
                      >
                        <Minus className="h-3 w-3" />
                      </Button>
                    )}

                    {isInCart ? (
                      <span className="mx-2 font-medium">{itemQuantityInCart}</span>
                    ) : quantity > 1 ? (
                      <span className="mx-2 font-medium">{quantity}</span>
                    ) : null}

                    <Button
                      variant={isInCart ? "outline" : "default"}
                      size="icon"
                      className={`h-7 w-7 ${isInCart ? "" : "bg-emerald-600 hover:bg-emerald-700 text-white"}`}
                      onClick={needsDialog ? handleOpenDialog : (isInCart ? incrementQuantity : handleAddToCart)}
                    >
                      {isInCart ? <Plus className="h-3 w-3" /> : <Plus className="h-3 w-3" />}
                    </Button>
                  </div>
                </div>
              </div>

              {product.image && (
                <div className="w-24 h-24 sm:w-32 sm:h-32 flex-shrink-0">
                  <FallbackImage
                    src={product.image}
                    alt={product.name}
                    fallbackSrc="/placeholder.svg"
                    className="w-full h-full object-contain"
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Customization Dialog */}
        {needsDialog && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{product.name}</DialogTitle>
              </DialogHeader>
              <div className="space-y-6 py-4">
                {/* Variant Selection */}
                {hasVariants && (
                  <div className="space-y-4">
                    <h3 className="font-medium">Size</h3>
                    <RadioGroup
                      value={selectedVariant?.id?.toString() || ""}
                      onValueChange={(value) => {
                        const variant = product.variants?.find(v => v.id?.toString() === value);
                        setSelectedVariant(variant);
                        calculateTotalPrice(selectedOptions);
                      }}
                    >
                      {product.variants?.map((variant) => (
                        <div key={variant.id} className="flex items-center space-x-2">
                          <RadioGroupItem
                            value={variant.id?.toString() || ""}
                            id={`variant-${variant.id}`}
                          />
                          <Label htmlFor={`variant-${variant.id}`} className="flex-1">
                            {variant.name}
                            <span className="text-gray-500 ml-1">
                              £{variant.price?.toFixed(2)}
                            </span>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>
                )}

                {/* Customizations */}
                {product.customizations?.map((customization) => (
                  <div key={customization.id} className="space-y-4">
                    <h3 className="font-medium">
                      {customization.name}
                      {customization.required && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </h3>
                    {customization.isMultiple ? (
                      // Checkbox group for multiple selection
                      <div className="space-y-2">
                        {customization.options.map((option) => (
                          <div key={option.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`${customization.id}-${option.id}`}
                              checked={selectedOptions[customization.id]?.includes(option.id) || false}
                              onCheckedChange={() => handleOptionChange(customization.id, option.id, true)}
                            />
                            <Label htmlFor={`${customization.id}-${option.id}`} className="flex-1">
                              {option.name}
                              {option.price > 0 && (
                                <span className="text-gray-500 ml-1">
                                  +£{option.price.toFixed(2)}
                                </span>
                              )}
                            </Label>
                          </div>
                        ))}
                      </div>
                    ) : (
                      // Radio group for single selection
                      <RadioGroup
                        value={selectedOptions[customization.id]?.[0] || ""}
                        onValueChange={(value) => handleOptionChange(customization.id, value, false)}
                      >
                        {customization.options.map((option) => (
                          <div key={option.id} className="flex items-center space-x-2">
                            <RadioGroupItem
                              value={option.id}
                              id={`${customization.id}-${option.id}`}
                            />
                            <Label htmlFor={`${customization.id}-${option.id}`} className="flex-1">
                              {option.name}
                              {option.price > 0 && (
                                <span className="text-gray-500 ml-1">
                                  +£{option.price.toFixed(2)}
                                </span>
                              )}
                            </Label>
                          </div>
                        ))}
                      </RadioGroup>
                    )}
                  </div>
                ))}
              </div>
              <div className="flex justify-between items-center">
                <p className="font-medium">Total: £{totalPrice.toFixed(2)}</p>
                <Button onClick={() => handleAddToCart(getSelectedOptionsText())}>
                  Add to Basket
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </>
    );
  }
}
